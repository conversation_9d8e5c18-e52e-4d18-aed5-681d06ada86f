#!/usr/bin/env python3
"""
测试主页面的引用功能
"""

import requests
import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import sys

def test_main_page_citation():
    """测试主页面的引用功能"""
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://127.0.0.1:8000")
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 等待输入框出现
        input_box = wait.until(EC.presence_of_element_located((By.TAG_NAME, "textarea")))
        
        print("✅ 主页面加载成功")
        
        # 输入测试问题
        test_question = "电子发票重复报销如何防范？"
        input_box.send_keys(test_question)
        
        # 点击发送按钮
        send_button = driver.find_element(By.XPATH, "//button[contains(@class, 'bg-blue-500')]")
        send_button.click()
        
        print(f"✅ 发送问题: {test_question}")
        
        # 等待响应（最多30秒）
        try:
            # 等待新消息出现
            wait.until(lambda driver: len(driver.find_elements(By.XPATH, "//div[contains(@class, 'bg-gray-100')]")) > 1)
            print("✅ 收到AI响应")
            
            # 检查是否有引用数字
            citation_numbers = driver.find_elements(By.CLASS_NAME, "citation-number")
            
            if citation_numbers:
                print(f"✅ 发现 {len(citation_numbers)} 个引用数字")
                
                # 测试悬浮功能
                first_citation = citation_numbers[0]
                driver.execute_script("arguments[0].scrollIntoView();", first_citation)
                
                # 模拟鼠标悬浮
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(driver)
                actions.move_to_element(first_citation).perform()
                
                # 检查悬浮框是否出现
                time.sleep(1)  # 等待悬浮框出现
                
                tooltip = driver.find_element(By.CLASS_NAME, "citation-tooltip")
                if tooltip.is_displayed():
                    print("✅ 引用悬浮框显示成功")
                    
                    # 获取悬浮框内容
                    tooltip_text = tooltip.text
                    print(f"📄 悬浮框内容预览: {tooltip_text[:100]}...")
                    
                    return True
                else:
                    print("❌ 引用悬浮框未显示")
                    return False
            else:
                print("❌ 未发现引用数字")
                
                # 检查页面内容
                page_content = driver.find_element(By.TAG_NAME, "body").text
                if "Source" in page_content:
                    print("⚠️  页面包含Source标记，但未转换为引用数字")
                else:
                    print("⚠️  页面不包含引用内容")
                
                return False
                
        except Exception as e:
            print(f"❌ 等待响应超时或出错: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False
    finally:
        if 'driver' in locals():
            driver.quit()

def test_api_directly():
    """直接测试API响应"""
    print("\n🔧 直接测试API...")
    
    url = "http://localhost:8000/api/chat"
    payload = {
        "id": f"test_main_{int(time.time())}",
        "messages": [
            {
                "role": "user",
                "content": "电子发票重复报销如何防范？"
            }
        ],
        "data": {}
    }
    
    try:
        response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, timeout=30)
        
        if response.status_code == 200:
            response_text = response.text
            
            # 检查引用数据
            if "CITATION_DATA:" in response_text:
                print("✅ API返回包含引用数据")
                
                # 检查Source标记
                if "Source 1:" in response_text or "Source 2:" in response_text:
                    print("✅ API返回包含Source标记")
                    return True
                else:
                    print("⚠️  API返回包含引用数据但无Source标记")
                    return False
            else:
                print("❌ API返回不包含引用数据")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试主页面引用功能...")
    print("=" * 50)
    
    # 首先测试API
    api_success = test_api_directly()
    
    if api_success:
        print("\n🌐 测试前端页面...")
        try:
            frontend_success = test_main_page_citation()
            
            if frontend_success:
                print("\n🎉 所有测试通过！引用功能正常工作。")
            else:
                print("\n⚠️  前端测试失败，但API正常。可能是前端处理问题。")
        except Exception as e:
            print(f"\n⚠️  无法运行前端测试（可能缺少Selenium）: {e}")
            print("请手动在浏览器中测试: http://127.0.0.1:8000")
    else:
        print("\n❌ API测试失败，请检查后端服务。")
    
    print("\n💡 手动测试步骤:")
    print("1. 打开 http://127.0.0.1:8000")
    print("2. 输入问题：'电子发票重复报销如何防范？'")
    print("3. 查看回答中是否有蓝色数字引用")
    print("4. 鼠标悬浮在数字上查看是否显示文档内容")
